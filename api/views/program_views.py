import traceback
from typing import List, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, cast, String
from models.program_models import (
    Program,
    SubRoutine,
    ProgramCategory,
    ProgramVersion,
    ProgramStatus,
)
from schemas.program_schemas import ProgramUpdate
from models import Product, User
from models.account_models import User



# --- View Functions for Frontend --- #


def get_program_categories_for_product(db: Session, product_id: int):
    """
    Retrieves Program Categories associated with a specific product,
    calculating the count of programs within each category for that product.
    Returns a list of dictionaries matching ProductCategoryWithCountSchema structure.
    """
    try:
        # Query ProgramCategory and count associated Programs filtered by product_id
        results = (
            db.query(
                ProgramCategory.id,
                ProgramCategory.name,
                ProgramCategory.description,
                ProgramCategory.created_at,
                ProgramCategory.updated_at,
                func.count(Program.id).label("program_count"),
            )
            .select_from(ProgramCategory)  # Explicitly start from ProgramCategory
            .outerjoin(
                Program,
                (Program.category_id == ProgramCategory.id)
                & (Program.product_id == product_id),
            )
            # Filter to only include categories that *have* programs for this product
            # Or, group by all categories and have counts be 0? Let's include only relevant ones.
            # Do NOT filter after the join; this would exclude categories with zero programs.
            .group_by(
                ProgramCategory.id,
                ProgramCategory.name,
                ProgramCategory.description,
                ProgramCategory.created_at,
                ProgramCategory.updated_at,
            )
            .order_by(ProgramCategory.name)  # Optional: order by name
            .all()
        )

        # Convert SQLAlchemy results to list of dictionaries matching the schema
        # Use snake_case for created_at/updated_at as defined in BaseSchema
        # Use alias programCount as defined in ProductCategoryWithCountSchema
        categories_with_counts = [
            {
                "id": cat_id,
                "name": name,
                "description": desc,
                "created_at": created,  # Use snake_case
                "updated_at": updated,  # Use snake_case
                "programCount": p_count,  # Use alias from schema
            }
            for cat_id, name, desc, created, updated, p_count in results
        ]
        return categories_with_counts

    except Exception as e:
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve program categories for product {product_id}",
        )


def list_subroutines_paginated(
    db: Session,
    skip: int = 0,
    limit: int = 20,
    product_id: int = None,
    search: str = None,
    sort_field: str = "created_at",
    sort_direction: str = "desc",
):
    """
    Retrieves a paginated list of subroutines with optional filtering by product_id, search, and sorting.
    Returns a dictionary with 'subroutines' list and 'total' count for pagination.
    """
    try:
        # Base query
        query = db.query(
            SubRoutine.id,
            SubRoutine.name,
            SubRoutine.subroutine_id_json,
            SubRoutine.product_id,
            Product.name.label("product_name"),
            func.coalesce(func.json_array_length(SubRoutine.steps), 0).label(
                "actions_count"
            ),
            SubRoutine.created_at,
            SubRoutine.updated_at,
        ).join(Product, SubRoutine.product_id == Product.id)

        # Apply product filter if specified
        if product_id:
            query = query.filter(SubRoutine.product_id == product_id)

        # Apply search filter if specified
        if search and search.strip():
            search_term = f"%{search.strip()}%"
            query = query.filter(
                (SubRoutine.name.ilike(search_term))
                | (SubRoutine.subroutine_id_json.ilike(search_term))
            )

        # Determine sort field and direction
        sort_field_map = {
            "id": SubRoutine.id,
            "name": SubRoutine.name,
            "subroutine_id_json": SubRoutine.subroutine_id_json,
            "product_id": SubRoutine.product_id,
            "actions_count": func.coalesce(func.json_array_length(SubRoutine.steps), 0),
            "created_at": SubRoutine.created_at,
            "updated_at": SubRoutine.updated_at,
        }
        sort_col = sort_field_map.get(sort_field, SubRoutine.created_at)
        if sort_direction and sort_direction.lower() == "asc":
            query = query.order_by(sort_col.asc())
        else:
            query = query.order_by(sort_col.desc())

        # Get total count for pagination (after filters, before pagination)
        total = query.count()

        # Apply pagination
        subroutines_db = query.offset(skip).limit(limit).all()

        # Transform results to match schema
        subroutines_list = [
            {
                "id": sub.id,
                "name": sub.name,
                "subroutineIdJson": sub.subroutine_id_json,
                "product_id": sub.product_id,
                "product_name": sub.product_name,
                "actionsCount": sub.actions_count or 0,
                "created_at": sub.created_at,
                "updated_at": sub.updated_at,
            }
            for sub in subroutines_db
        ]

        return {"subroutines": subroutines_list, "total": total}

    except Exception as e:
        print(f"Error fetching paginated subroutines: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve paginated subroutines: {str(e)}",
        )


def get_subroutine_by_id(db: Session, subroutine_id: int):
    """
    Retrieve a specific subroutine by its database ID.
    Returns the SubRoutine object or raises an HTTPException if not found.
    """
    subroutine = db.query(SubRoutine).filter(SubRoutine.id == subroutine_id).first()
    if not subroutine:
        raise HTTPException(status_code=404, detail="Subroutine not found")
    return subroutine


def get_subroutine_by_json_id(db: Session, subroutine_id_json: str):
    """
    Retrieve a specific subroutine by its JSON ID (subroutine_id_json field).
    Returns the SubRoutine object or raises an HTTPException if not found.
    """
    subroutine = db.query(SubRoutine).filter(SubRoutine.subroutine_id_json == subroutine_id_json).first()
    if not subroutine:
        raise HTTPException(status_code=404, detail=f"Subroutine with JSON ID '{subroutine_id_json}' not found")
    return subroutine


def list_programs_by_category(
    db: Session,
    category_id: int,
    skip: int = 0,
    limit: int = 20,
    product_id: int = None,
    search: str = None,
    sort_field: str = "created_at",
    sort_direction: str = "desc",
):
    """
    Retrieves a paginated list of programs for a specific category with optional filtering by product_id.
    Supports searching by program title or name and sorting by different fields.
    Returns a dictionary with 'programs' list and 'total' count for pagination.
    """
    try:
        # Base query
        query = (
            db.query(
                Program.id,
                Program.name,
                Program.program_title,
                Program.source_folder,
                Program.source_filename,
                Program.product_id,
                Product.name.label("product_name"),
                Program.user_id,
                User.username.label("user_name"),
                Program.created_at,
                Program.updated_at,
                Program.status,
            )
            .join(Product, Program.product_id == Product.id)
            .outerjoin(User, Program.user_id == User.id)
        )

        # Filter by category
        query = query.filter(Program.category_id == category_id)

        # Apply product filter if specified
        if product_id:
            query = query.filter(Program.product_id == product_id)

        # Apply search filter if specified
        if search and search.strip():
            search_term = f"%{search.strip()}%"
            query = query.filter(
                (Program.program_title.ilike(search_term))
                | (Program.name.ilike(search_term))
            )

        # Get total count for pagination
        total = query.count()

        # Determine sort field and direction
        valid_sort_fields = {
            "id": Program.id,
            "name": Program.name,
            "program_title": Program.program_title,
            "source_filename": Program.source_filename,
            "product_name": Product.name,
            "created_at": Program.created_at,
            "updated_at": Program.updated_at,
        }

        # Default to created_at if invalid sort field
        sort_column = valid_sort_fields.get(sort_field, Program.created_at)

        # Apply sort direction
        if sort_direction.lower() == "asc":
            sort_column = sort_column.asc()
        else:
            sort_column = sort_column.desc()

        # Apply pagination with sorting
        programs_db = query.order_by(sort_column).offset(skip).limit(limit).all()

        # Transform results to match schema
        programs_list = [
            {
                "id": prog.id,
                "name": prog.name,
                "program_title": prog.program_title,
                "source_folder": prog.source_folder,
                "source_filename": prog.source_filename,
                "product_id": prog.product_id,
                "product_name": prog.product_name,
                "user_id": prog.user_id,
                "user_name": prog.user_name,
                "created_at": prog.created_at,
                "updated_at": prog.updated_at,
                "status": prog.status,
            }
            for prog in programs_db
        ]

        return {"programs": programs_list, "total": total}

    except Exception as e:
        print(f"Error fetching paginated programs: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve programs for category {category_id}: {str(e)}",
        )


# --- Program Detail Functions ---


def get_program_by_id(db: Session, program_id: int):
    """
    Retrieve a specific program by its ID, including associated subroutines.
    Returns the program object or raises an HTTPException if not found.
    """
    try:
        # Query the program with its associated subroutines, product, category, and versions
        program = (
            db.query(Program)
            .options(
                joinedload(Program.product),
                joinedload(Program.category),
                joinedload(Program.sub_routines),
                joinedload(Program.versions).joinedload(ProgramVersion.user),
            )
            .filter(Program.id == program_id)
            .first()
        )

        if not program:
            raise HTTPException(
                status_code=404, detail=f"Program with ID {program_id} not found"
            )

        return program

    except Exception as e:
        print(f"Error fetching program with ID {program_id}: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve program: {str(e)}"
        )


def update_program(
    db: Session, program_id: int, program_data: ProgramUpdate, user: User
):
    """
    Update an existing program.
    A new version of the program is created if and only if the 'steps' field has changed.
    If steps are changed, a new ProgramVersion record is created, and the program's
    active steps and current_version_number are updated.
    Fields such as 'name', 'program_title', 'logic_technique', and 'program_description'
    are updated directly on the Program object if provided and changed. These changes alone
    do not trigger a new version.
    The 'version_notes' are applied to the new version if one is created.
    Returns a dictionary containing the updated program object and a flag indicating
    if an embedding update is needed (triggered by program_description change).
    """
    embedding_update_needed = False
    made_db_changes = False
    new_version_obj_for_refresh = None

    try:
        program = (
            db.query(Program)
            .options(
                joinedload(Program.product),
                joinedload(Program.category),
                joinedload(Program.sub_routines),
                joinedload(Program.versions).joinedload(ProgramVersion.user),
            )
            .filter(Program.id == program_id)
            .first()
        )
        if not program:
            raise HTTPException(
                status_code=404, detail=f"Program with ID {program_id} not found"
            )

        # Versioning logic: A new version is created ONLY if steps have actually changed.
        if program_data.steps is not None and program.steps != program_data.steps:
            new_version_number = program.current_version_number + 1
            new_version = ProgramVersion(
                program_id=program.id,
                version_number=new_version_number,
                steps=program_data.steps,
                version_notes=program_data.version_notes,  # Used if creating a version
                created_by_user_id=user.id,
            )
            db.add(new_version)
            new_version_obj_for_refresh = new_version

            program.current_version_number = new_version_number
            program.steps = program_data.steps  # Update program's active steps
            made_db_changes = True

        # Update other program fields directly on the Program object
        # These updates do NOT trigger a new version on their own.
        if program_data.name is not None and program.name != program_data.name:
            program.name = program_data.name
            made_db_changes = True

        if (
            program_data.program_title is not None
            and program.program_title != program_data.program_title
        ):
            program.program_title = program_data.program_title
            made_db_changes = True

        if (
            program_data.logic_technique is not None
            and program.logic_technique != program_data.logic_technique
        ):
            program.logic_technique = program_data.logic_technique
            made_db_changes = True

        if (
            program_data.program_description is not None
            and program.program_description != program_data.program_description
        ):
            program.program_description = program_data.program_description
            embedding_update_needed = True
            made_db_changes = True

        if made_db_changes:
            db.commit()
            db.refresh(program)
            if new_version_obj_for_refresh:
                db.refresh(new_version_obj_for_refresh)

        return {"program": program, "embedding_update_needed": embedding_update_needed}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"Error updating program with ID {program_id}: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Failed to update program: {str(e)}"
        )


def update_program_status(db: Session, program_id: int, status: ProgramStatus):
    """
    Update the status of a specific program.
    """
    program = db.query(Program).filter(Program.id == program_id).first()
    if not program:
        raise HTTPException(status_code=404, detail="Program not found")

    program.status = status
    db.commit()
    db.refresh(program)
    return program


# --- Function to get a specific program version's steps ---
def get_program_version_steps_by_version_number(
    db: Session, program_id: int, version_number: int
):
    # Query for the specific program version
    version = (
        db.query(ProgramVersion)
        .options(joinedload(ProgramVersion.user))  # Eager load user
        .filter(
            ProgramVersion.program_id == program_id,
            ProgramVersion.version_number == version_number,
        )
        .first()
    )

    if not version:
        return None

    # Prepare the data for the schema
    version_data = {
        "id": version.id,
        "program_id": version.program_id,
        "version_number": version.version_number,
        "steps": version.steps,
        "created_at": version.created_at,
        "created_by_user_id": version.created_by_user_id,
        "created_by_username": (
            version.user.username if version.user else None
        ),  # Add username
        "version_notes": version.version_notes,
    }
    return version_data


def delete_program_version_by_number(
    db: Session, program_id: int, version_number: int, user_id: int
):
    # Fetch the program version
    program_version = (
        db.query(ProgramVersion)
        .filter(
            ProgramVersion.program_id == program_id,
            ProgramVersion.version_number == version_number,
        )
        .first()
    )

    if not program_version:
        raise HTTPException(status_code=404, detail="Program version not found")

    # Fetch the parent program
    program = db.query(Program).filter(Program.id == program_id).first()
    if not program:
        # This case should ideally not happen if a version exists, but good to check
        raise HTTPException(status_code=404, detail="Program not found")

    # Check if the version is the current active version
    if program_version.version_number == program.current_version_number:
        raise HTTPException(
            status_code=400,
            detail="The current active version of a program cannot be deleted.",
        )

    # Check if the user attempting to delete is the creator of the version
    # Allow deletion if created_by_user_id is None (e.g. system generated, though current logic sets it)
    # Or if it matches the current user's ID.
    if (
        program_version.created_by_user_id is not None
        and program_version.created_by_user_id != user_id
    ):
        raise HTTPException(
            status_code=403,
            detail="You are not authorized to delete this program version.",
        )

    # Delete the program version
    db.delete(program_version)
    db.commit()

    return {"message": "Program version deleted successfully"}


# --- Bulk Embedding Processing Functions ---


def get_programs_for_bulk_embedding(
    db: Session,
    product_id: Optional[int] = None,
    category_id: Optional[int] = None,
    limit: int = 100,
) -> List[int]:
    """
    Get program IDs for bulk embedding processing with optional filtering.
    Only returns programs that have a valid program_description with
    programme_sequence and signature_moves fields for better embedding results.

    Args:
        db: Database session
        product_id: Optional filter by product ID
        category_id: Optional filter by category ID
        limit: Maximum number of programs to return

    Returns:
        List of program IDs
    """
    try:
        # Base query for program IDs
        query = db.query(Program.id)

        # Apply filters if provided
        if product_id is not None:
            query = query.filter(Program.product_id == product_id)

        if category_id is not None:
            query = query.filter(Program.category_id == category_id)

        # Filter to only include programs with valid program_description
        # that contains both programme_sequence and signature_moves
        query = query.filter(
            # Ensure program_description is not null
            Program.program_description.isnot(None),
            # Check for programme_sequence key and validate it's not empty
            Program.program_description["programme_sequence"] != None,
            # Also check that programme_sequence is not an empty object
            cast(Program.program_description["programme_sequence"], String).notlike(
                "{}"
            ),
            # Check for signature_moves key and validate it's not empty
            Program.program_description["signature_moves"] != None,
            # Also check that signature_moves is not an empty array
            cast(Program.program_description["signature_moves"], String).notlike("[]"),
        )

        # Limit the number of programs
        query = query.limit(limit)

        # Extract IDs from result
        program_ids = [program_id for (program_id,) in query.all()]

        return program_ids

    except Exception as e:
        print(f"Error fetching programs for bulk embedding: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve programs for bulk embedding: {str(e)}",
        )


# --- Program Version Migration Function ---
async def migrate_programs_to_versioning_handler(db: Session):
    """
    Migrate existing programs to use the versioning system.
    For each program without versions:
    1. Set current_version_number = 1 if not already set
    2. Create a ProgramVersion with version_number = 1 containing the program's steps
    """
    try:
        # Get all programs
        programs = db.query(Program).all()

        migrated_count = 0
        skipped_count = 0

        for program in programs:
            # Check if the program already has versions
            existing_versions = (
                db.query(ProgramVersion)
                .filter(ProgramVersion.program_id == program.id)
                .count()
            )

            if existing_versions > 0:
                skipped_count += 1
                continue

            # Ensure current_version_number is set
            if not program.current_version_number:
                program.current_version_number = 1

            # Create initial version
            initial_version = ProgramVersion(
                program_id=program.id,
                version_number=program.current_version_number,
                steps=program.steps,
                version_notes="Initial version created during migration.",
            )

            db.add(initial_version)
            migrated_count += 1

        # Commit all changes
        db.commit()

        return {
            "success": True,
            "message": f"Migration completed successfully. {migrated_count} programs migrated, {skipped_count} programs already had versions.",
        }

    except Exception as e:
        db.rollback()
        print(f"Error during program version migration: {e}")
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to migrate programs to versioning: {str(e)}",
        )
