'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronRight,
  Activity,
  ArrowUp,
  ArrowDown,
  MoveRight,
  Layers
} from 'lucide-react';

import { ProgramStepDetails } from './program-step-details';
import { Badge } from '@/components/ui/badge';
import { groupStepsByType, STEP_TYPE_CONFIG } from '@/utils/step-type-classifier';
import type { ClassificationResult, FlowGroup, SubroutineFlow } from '@/utils/body-part-classifier';

interface ProgramFlowViewProps {
  classificationResult: ClassificationResult;
}

const FlowIndicator = ({
  direction,
  isFirst,
  isLast
}: {
  direction: FlowGroup['direction'];
  isFirst: boolean;
  isLast: boolean;
}) => {
  if (direction === 'end') return null;

  const getDirectionConfig = () => {
    switch (direction) {
      case 'down':
        return {
          icon: <ArrowDown className="h-3 w-3" />,
          color: 'text-teal-600',
          lineColor: 'border-teal-300'
        };
      case 'up':
        return {
          icon: <ArrowUp className="h-3 w-3" />,
          color: 'text-amber-600',
          lineColor: 'border-amber-300'
        };
      case 'same':
        return {
          icon: <MoveRight className="h-3 w-3" />,
          color: 'text-blue-600',
          lineColor: 'border-blue-300'
        };
      default:
        return {
          icon: null,
          color: 'text-muted-foreground',
          lineColor: 'border-muted'
        };
    }
  };

  const config = getDirectionConfig();

  return (
    <div className="absolute left-3 top-0 bottom-0 flex flex-col items-center">
      {/* Vertical line spanning the entire flow group */}
      <div className={`w-0.5 flex-1 border-l-2 ${config.lineColor}`} />
      
      {/* Direction indicator positioned in the middle of the flow group */}
      {!isLast && (
        <div className="absolute top-1/2 transform -translate-y-1/2 bg-background rounded-full p-1.5 border shadow-sm">
          <div className={config.color}>
            {config.icon}
          </div>
        </div>
      )}
    </div>
  );
};

const SubroutineCard = ({
  subroutineFlow,
  expandedGroups,
  toggleGroupExpansion
}: {
  subroutineFlow: SubroutineFlow;
  expandedGroups: Set<number>;
  toggleGroupExpansion: (index: number) => void;
}) => {
  const [isSubroutineExpanded, setIsSubroutineExpanded] = useState(false);

  if (subroutineFlow.bodyPartGroups.length === 0) {
    return (
      <Card className="border-l-4 border-l-purple-300 bg-purple-50/30">
        <CardHeader className="py-3">
          <div className="flex items-center gap-2">
            <div className="p-1 bg-purple-100 rounded border border-purple-200">
              <Layers className="h-3 w-3 text-purple-600" />
            </div>
            <div>
              <div className="text-sm font-medium text-purple-800">
                {subroutineFlow.subroutineName}
              </div>
              <div className="text-xs text-purple-600">
                Step {subroutineFlow.stepNumber} • Subroutine • No body part groups
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="border-l-4 border-l-purple-300 bg-purple-50/30">
      <Collapsible open={isSubroutineExpanded} onOpenChange={setIsSubroutineExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-purple-100/50 transition-colors py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 flex-1">
                {isSubroutineExpanded ? (
                  <ChevronDown className="h-3 w-3 text-muted-foreground" />
                ) : (
                  <ChevronRight className="h-3 w-3 text-muted-foreground" />
                )}
                <div className="p-1 bg-purple-100 rounded border border-purple-200">
                  <Layers className="h-3 w-3 text-purple-600" />
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-purple-800">
                    {subroutineFlow.subroutineName}
                  </div>
                  <div className="text-xs text-purple-600 mb-2">
                    Step {subroutineFlow.stepNumber} • Subroutine • {subroutineFlow.totalSteps} internal steps
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {subroutineFlow.flowGroups.map((flowGroup, index) => (
                      <Badge key={index} variant="outline" className="text-xs bg-purple-100 text-purple-700 border-purple-300">
                        {flowGroup.direction === 'down' ? '↓' : flowGroup.direction === 'up' ? '↑' : '→'} {flowGroup.bodyPartGroups.length}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="pt-0 pl-8">
            <div className="space-y-2">
              {subroutineFlow.flowGroups.map((flowGroup, flowIndex) => (
                <div key={flowIndex} className="space-y-2">
                  {flowGroup.direction !== 'end' && (
                    <div className="mb-2">
                      <div className="flex items-center gap-2 text-xs">
                        <div className={`w-1.5 h-1.5 rounded-full ${
                          flowGroup.direction === 'down' ? 'bg-teal-500' :
                          flowGroup.direction === 'up' ? 'bg-amber-500' : 'bg-blue-500'
                        }`} />
                        <span className={`font-medium ${
                          flowGroup.direction === 'down' ? 'text-teal-700' :
                          flowGroup.direction === 'up' ? 'text-amber-700' : 'text-blue-700'
                        }`}>
                          {flowGroup.direction === 'down' ? 'Moving Down' :
                           flowGroup.direction === 'up' ? 'Moving Up' : 'Same Level'}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {flowGroup.bodyPartGroups.length} group{flowGroup.bodyPartGroups.length !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                    </div>
                  )}

                  {flowGroup.bodyPartGroups.map((group, groupIndex) => {
                    // Use a unique key for subroutine groups to avoid conflicts with main flow
                    const globalIndex = `subroutine-${subroutineFlow.subroutineId}-${flowIndex}-${groupIndex}`;
                    const isExpanded = expandedGroups.has(globalIndex as any);

                    return (
                      <Card key={globalIndex} className="border-l-4 border-l-purple-200 hover:border-l-purple-300 transition-colors ml-4">
                        <Collapsible open={isExpanded} onOpenChange={() => toggleGroupExpansion(globalIndex as any)}>
                          <CollapsibleTrigger asChild>
                            <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors py-2">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2 flex-1">
                                  {isExpanded ? (
                                    <ChevronDown className="h-3 w-3 text-muted-foreground" />
                                  ) : (
                                    <ChevronRight className="h-3 w-3 text-muted-foreground" />
                                  )}
                                  <div className="flex-1">
                                    <div className="text-sm font-medium">
                                      {group.displayName}
                                    </div>
                                    <div className="text-xs text-muted-foreground mb-2">
                                      Steps {group.startStepNumber}-{group.endStepNumber} • {group.stepCount} steps
                                    </div>
                                    {/* Technique Summary */}
                                    {(() => {
                                      const stepTypeResult = groupStepsByType(group.steps);
                                      const uniqueTechniques = Object.entries(stepTypeResult.techniqueDistribution)
                                        .filter(([type, count]) => count > 0 && type !== 'unknown')
                                        .sort(([,a], [,b]) => b - a)
                                        .slice(0, 4); // Show top 4 techniques

                                      return uniqueTechniques.length > 0 ? (
                                        <div className="flex flex-wrap gap-1">
                                          {uniqueTechniques.map(([type, count]) => {
                                            const config = STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG];
                                            return (
                                              <Badge key={type} variant="secondary" className="text-xs">
                                                {config.icon} {count}
                                              </Badge>
                                            );
                                          })}
                                        </div>
                                      ) : null;
                                    })()}
                                  </div>
                                </div>
                              </div>
                            </CardHeader>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                            <CardContent className="pt-0">
                              <ProgramStepDetails group={group} />
                            </CardContent>
                          </CollapsibleContent>
                        </Collapsible>
                      </Card>
                    );
                  })}
                </div>
              ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export function ProgramFlowView({ classificationResult }: ProgramFlowViewProps) {
  const [expandedGroups, setExpandedGroups] = useState<Set<number>>(new Set());

  const toggleGroupExpansion = (groupIndex: number) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupIndex)) {
      newExpanded.delete(groupIndex);
    } else {
      newExpanded.add(groupIndex);
    }
    setExpandedGroups(newExpanded);
  };

  const { flowGroups, subroutineFlows } = classificationResult;

  if (flowGroups.length === 0 && subroutineFlows.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Program Groups</h3>
            <p className="text-muted-foreground">
              No program groups could be identified from the program steps.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <div className="p-1 bg-emerald-50 rounded border border-emerald-200">
            <Activity className="h-3 w-3 text-emerald-600" />
          </div>
          <div>
            <h3 className="text-sm font-medium">Program Flow</h3>
            <p className="text-xs text-muted-foreground">
              Visual timeline showing focus on different body parts
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>

        <div className="space-y-4">
          {/* Main Flow Groups */}
          {flowGroups.map((flowGroup, flowIndex) => (
            <div key={flowIndex} className="relative pl-8">
              {/* Flow indicator that spans the entire group */}
              <FlowIndicator
                direction={flowGroup.direction}
                isFirst={flowIndex === 0}
                isLast={flowIndex === flowGroups.length - 1}
              />
              
              {/* Flow group header */}
              {flowGroup.direction !== 'end' && (
                <div className="mb-3 pl-2">
                  <div className="flex items-center gap-2 text-xs">
                    <div className={`w-1.5 h-1.5 rounded-full ${
                      flowGroup.direction === 'down' ? 'bg-teal-500' :
                      flowGroup.direction === 'up' ? 'bg-amber-500' : 'bg-blue-500'
                    }`} />
                    <span className={`font-medium ${
                      flowGroup.direction === 'down' ? 'text-teal-700' :
                      flowGroup.direction === 'up' ? 'text-amber-700' : 'text-blue-700'
                    }`}>
                      {flowGroup.direction === 'down' ? 'Moving Down' :
                       flowGroup.direction === 'up' ? 'Moving Up' : 'Same Level'}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {flowGroup.bodyPartGroups.length} group{flowGroup.bodyPartGroups.length !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                </div>
              )}
            
              <div className="space-y-2">
                {flowGroup.bodyPartGroups.map((group, groupIndex) => {
                  const globalIndex = classificationResult.bodyPartGroups.indexOf(group);
                  const isExpanded = expandedGroups.has(globalIndex);

                  return (
                    <Card key={globalIndex} className="border-l-4 border-l-blue-200 hover:border-l-blue-300 transition-colors">
                      <Collapsible open={isExpanded} onOpenChange={() => toggleGroupExpansion(globalIndex)}>
                        <CollapsibleTrigger asChild>
                          <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors py-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2 flex-1">
                                {isExpanded ? (
                                  <ChevronDown className="h-3 w-3 text-muted-foreground" />
                                ) : (
                                  <ChevronRight className="h-3 w-3 text-muted-foreground" />
                                )}
                                <div className="flex-1">
                                  <div className="text-sm font-medium">
                                    {group.displayName}
                                  </div>
                                  <div className="text-xs text-muted-foreground mb-2">
                                    Steps {group.startStepNumber}-{group.endStepNumber} • {group.stepCount} steps
                                  </div>
                                  {/* Technique Summary */}
                                  {(() => {
                                    const stepTypeResult = groupStepsByType(group.steps);
                                    const uniqueTechniques = Object.entries(stepTypeResult.techniqueDistribution)
                                      .filter(([type, count]) => count > 0 && type !== 'unknown')
                                      .sort(([,a], [,b]) => b - a)
                                      .slice(0, 4); // Show top 4 techniques
                                    
                                    return (
                                      <div className="flex flex-wrap gap-1">
                                        {uniqueTechniques.map(([type, count]) => {
                                          const config = STEP_TYPE_CONFIG[type as keyof typeof STEP_TYPE_CONFIG];
                                          return (
                                            <Badge 
                                              key={type} 
                                              variant="secondary" 
                                              className="text-xs px-1.5 py-0.5 bg-indigo-50 text-indigo-700 border-indigo-200" 
                                              title={`${config.displayName}: ${count} steps`}
                                            >
                                              <span className="mr-0.5">{config.icon}</span>
                                              {count}
                                            </Badge>
                                          );
                                        })}
                                        {stepTypeResult.techniqueDistribution.unknown > 0 && (
                                          <Badge variant="outline" className="text-xs px-1.5 py-0.5 bg-amber-50 text-amber-700 border-amber-200">
                                            ❓ {stepTypeResult.techniqueDistribution.unknown}
                                          </Badge>
                                        )}
                                      </div>
                                    );
                                  })()}
                                </div>
                              </div>
                            </div>
                          </CardHeader>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                          <CardContent className="pt-0 pb-2">
                            <div className="border-t pt-2 overflow-hidden">
                              <ProgramStepDetails group={group} />
                            </div>
                          </CardContent>
                        </CollapsibleContent>
                      </Collapsible>
                    </Card>
                  );
                })}
              </div>
            </div>
          ))}

          {/* Subroutine Flow Cards */}
          {subroutineFlows.length > 0 && (
            <>
              {flowGroups.length > 0 && (
                <div className="border-t pt-4 mt-6">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="p-1 bg-purple-50 rounded border border-purple-200">
                      <Layers className="h-3 w-3 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-purple-800">Subroutines</h4>
                      <p className="text-xs text-purple-600">
                        Reusable step sequences with their own flow patterns
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                {subroutineFlows
                  .sort((a, b) => a.stepNumber - b.stepNumber)
                  .map((subroutineFlow, index) => (
                    <SubroutineCard
                      key={`${subroutineFlow.subroutineId}-${index}`}
                      subroutineFlow={subroutineFlow}
                      expandedGroups={expandedGroups}
                      toggleGroupExpansion={toggleGroupExpansion}
                    />
                  ))}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
