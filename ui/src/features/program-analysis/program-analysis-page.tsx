'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, BarChart3, Target, ChevronDown, ChevronRight, Activity } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

import { ProgramSelector } from '@/features/program-analysis/components/program-selector';
import { ProgramFlowView } from '@/features/program-analysis/components/program-flow-view';
import { ProgramStatsSummary } from '@/features/program-analysis/components/program-stats-summary';

import { useGetProgramById, useListSubroutines } from '@/queries/program';
import { groupStepsByBodyPart, getBodyPartStats } from '@/utils/body-part-classifier';
import type { Program } from 'types';

export function ProgramAnalysisPage() {
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  const [isProgramSelectorExpanded, setIsProgramSelectorExpanded] = useState(true);

  // Lift ProgramSelector state to prevent reset on collapse/expand
  const [selectedProductId, setSelectedProductId] = useState<number | undefined>();
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | undefined>();

  const {
    data: programData,
    isLoading: isProgramLoading,
    error: programError
  } = useGetProgramById(selectedProgram?.id || 0, {
    enabled: !!selectedProgram?.id
  });

  // Fetch all subroutines to match against subroutine steps
  const {
    data: allSubroutinesData
  } = useListSubroutines({
    limit: 1000 // Get all subroutines
  });

  // Process the program steps for body part grouping
  const classificationResult = programData?.steps
    ? groupStepsByBodyPart(
        programData.steps,
        programData.subRoutines,
        allSubroutinesData?.subroutines
      )
    : null;

  const bodyPartStats = classificationResult 
    ? getBodyPartStats(classificationResult)
    : null;

  return (
    <div className="space-y-4 w-full">
      {/* Header */}
      <div className="border-b pb-3">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-blue-50 rounded border border-blue-200">
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <h1 className="text-xl font-semibold tracking-tight">Program Analysis</h1>
            <p className="text-muted-foreground text-sm">
              Analyze and visualize program steps grouped by body parts
            </p>
          </div>
        </div>
      </div>

      {/* Program Selection */}
      <Card>
        <Collapsible open={isProgramSelectorExpanded} onOpenChange={setIsProgramSelectorExpanded}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/30 transition-colors py-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {isProgramSelectorExpanded ? (
                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  )}
                  <div className="p-1 bg-green-50 rounded border border-green-200">
                    <Target className="h-3 w-3 text-green-600" />
                  </div>
                  <div>
                    <CardTitle className="text-sm font-medium">Program Selection</CardTitle>
                    {selectedProgram ? (
                      <p className="text-xs text-muted-foreground">
                        {selectedProgram.program_title || selectedProgram.name}
                      </p>
                    ) : (
                      <p className="text-xs text-muted-foreground">
                        Select a program to analyze its steps and groupings
                      </p>
                    )}
                  </div>
                </div>
                {selectedProgram && (
                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 border-green-200">
                    #{selectedProgram.id}
                  </Badge>
                )}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0 pb-3">
              <ProgramSelector
                selectedProgram={selectedProgram}
                onProgramSelect={setSelectedProgram}
                selectedProductId={selectedProductId}
                onProductIdChange={setSelectedProductId}
                selectedCategoryId={selectedCategoryId}
                onCategoryIdChange={setSelectedCategoryId}
              />
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Program Analysis Results */}
      {selectedProgram && (
        <>
          

          {/* Loading State */}
          {isProgramLoading && (
            <Card>
              <CardHeader className="pb-3">
                <Skeleton className="h-5 w-48" />
                <Skeleton className="h-3 w-96" />
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-24 w-full" />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error State */}
          {programError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load program data: {programError.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Analysis Results */}
          {classificationResult && bodyPartStats && !isProgramLoading && (
            <div className="grid grid-cols-1 xl:grid-cols-4 gap-4">
              {/* Statistics Panel */}
              <div className="xl:col-span-1">
                <ProgramStatsSummary
                  stats={bodyPartStats}
                  classificationResult={classificationResult}
                />
              </div>

              {/* Program Groups */}
              <div className="xl:col-span-3">
                <ProgramFlowView
                  classificationResult={classificationResult}
                />
              </div>
            </div>
          )}

          {/* No Steps Message */}
          {programData && (!programData.steps || programData.steps.length === 0) && !isProgramLoading && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This program has no steps to analyze.
              </AlertDescription>
            </Alert>
          )}
        </>
      )}

      {/* No Program Selected */}
      {!selectedProgram && (
        <Card>
          <CardContent className="text-center py-8">
            <div className="p-2 bg-blue-50 rounded-full w-fit mx-auto mb-3 border border-blue-200">
              <Activity className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-base font-medium mb-1">No Program Selected</h3>
            <p className="text-sm text-muted-foreground max-w-md mx-auto">
              Choose a program from the selection panel above to view its analysis and step groupings
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
