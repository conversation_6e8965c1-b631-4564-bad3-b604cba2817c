/**
 * Body Part Classification Utility
 * 
 * Classifies program steps based on en_roller_action_description field
 * into predefined body parts for massage chair programming.
 */

export type BodyPart = 
  | 'head'
  | 'neck_upper_head'
  | 'neck_upper'
  | 'neck'
  | 'shoulder_upper'
  | 'shoulder'
  | 'shoulder_lower'
  | 'shoulder_lower_2'
  | 'shoulder_blade_upper'
  | 'shoulder_blade'
  | 'shoulder_blade_lower'
  | 'upper_back'
  | 'mid_back'
  | 'lower_back'
  | 'waist'
  | 'hips'
  | 'unknown';

export type FlowDirection = 'up' | 'down' | 'same' | 'end';

export interface BodyPartGroup {
  bodyPart: BodyPart;
  steps: any[];
  stepCount: number;
  startStepNumber: number;
  endStepNumber: number;
  displayName: string;
  color: string;
  flowDirection: FlowDirection;
}

export interface FlowGroup {
  direction: FlowDirection;
  bodyPartGroups: BodyPartGroup[];
}

export interface SubroutineFlow {
  subroutineId: string;
  subroutineName: string;
  stepNumber: number; // Where this subroutine appears in main flow
  bodyPartGroups: BodyPartGroup[];
  flowGroups: FlowGroup[];
  totalSteps: number;
  totalGroups: number;
  unclassifiedSteps: number;
}

export interface ClassificationResult {
  bodyPartGroups: BodyPartGroup[];
  flowGroups: FlowGroup[];
  subroutineFlows: SubroutineFlow[];
  totalSteps: number;
  totalGroups: number;
  unclassifiedSteps: number;
}

// Body part display names and colors
export const BODY_PART_CONFIG: Record<BodyPart, { displayName: string; color: string }> = {
  head: { displayName: 'Head', color: 'bg-purple-100 text-purple-800 border-purple-200' },
  neck_upper_head: { displayName: 'Upper Neck (Head)', color: 'bg-indigo-100 text-indigo-800 border-indigo-200' },
  neck_upper: { displayName: 'Upper Neck', color: 'bg-blue-100 text-blue-800 border-blue-200' },
  neck: { displayName: 'Neck', color: 'bg-cyan-100 text-cyan-800 border-cyan-200' },
  shoulder_upper: { displayName: 'Upper Shoulder', color: 'bg-teal-100 text-teal-800 border-teal-200' },
  shoulder: { displayName: 'Shoulder', color: 'bg-green-100 text-green-800 border-green-200' },
  shoulder_lower: { displayName: 'Lower Shoulder', color: 'bg-lime-100 text-lime-800 border-lime-200' },
  shoulder_lower_2: { displayName: 'Lower Shoulder (2)', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  shoulder_blade_upper: { displayName: 'Upper Shoulder Blade', color: 'bg-amber-100 text-amber-800 border-amber-200' },
  shoulder_blade: { displayName: 'Shoulder Blade', color: 'bg-orange-100 text-orange-800 border-orange-200' },
  shoulder_blade_lower: { displayName: 'Lower Shoulder Blade', color: 'bg-red-100 text-red-800 border-red-200' },
  upper_back: { displayName: 'Upper Back', color: 'bg-pink-100 text-pink-800 border-pink-200' },
  mid_back: { displayName: 'Mid Back', color: 'bg-rose-100 text-rose-800 border-rose-200' },
  lower_back: { displayName: 'Lower Back', color: 'bg-violet-100 text-violet-800 border-violet-200' },
  waist: { displayName: 'Waist', color: 'bg-fuchsia-100 text-fuchsia-800 border-fuchsia-200' },
  hips: { displayName: 'Hips', color: 'bg-slate-100 text-slate-800 border-slate-200' },
  unknown: { displayName: 'Unknown/Other', color: 'bg-gray-100 text-gray-800 border-gray-200' }
};

const CLASSIFICATION_RULES: Array<{
  test: (desc: string) => boolean;
  part: BodyPart;
}> = [
  // More specific rules should come first
  { test: desc => desc.includes('head') && desc.includes('neck') && desc.includes('upper'), part: 'neck_upper_head' },
  { test: desc => desc.includes('head'), part: 'head' },
  { test: desc => desc.includes('neck') && desc.includes('upper'), part: 'neck_upper' },
  { test: desc => desc.includes('neck'), part: 'neck' },
  { test: desc => (desc.includes('shoulder blade') || desc.includes('shoulder-blade')) && (desc.includes('up to the top') || desc.includes('upper') || desc.includes('top')), part: 'shoulder_blade_upper' },
  { test: desc => (desc.includes('shoulder blade') || desc.includes('shoulder-blade')) && (desc.includes('down to') || desc.includes('lower') || desc.includes('down')), part: 'shoulder_blade_lower' },
  { test: desc => desc.includes('shoulder blade') || desc.includes('shoulder-blade'), part: 'shoulder_blade' },
  { test: desc => desc.includes('shoulder') && (desc.includes('upper') || desc.includes('top')), part: 'shoulder_upper' },
  { test: desc => desc.includes('shoulder') && (desc.includes('lower') || desc.includes('down')) && (desc.includes('2') || desc.includes('second')), part: 'shoulder_lower_2' },
  { test: desc => desc.includes('shoulder') && (desc.includes('lower') || desc.includes('down')), part: 'shoulder_lower' },
  { test: desc => desc.includes('shoulder'), part: 'shoulder' },
  { test: desc => desc.includes('back') && (desc.includes('upper') || desc.includes('top')), part: 'upper_back' },
  { test: desc => desc.includes('back') && desc.includes('lower'), part: 'lower_back' },
  { test: desc => desc.includes('back') && (desc.includes('mid') || desc.includes('middle')), part: 'mid_back' },
  { test: desc => desc.includes('back'), part: 'upper_back' }, // Default for back
  { test: desc => desc.includes('hip'), part: 'hips' },
  { test: desc => desc.includes('waist'), part: 'waist' },
];

/**
 * Classifies a single step based on its en_roller_action_description
 */
export function classifyStep(description: string | null | undefined): BodyPart {
  if (!description || typeof description !== 'string') {
    return 'unknown';
  }
  const desc = description.toLowerCase().trim();

  const foundRule = CLASSIFICATION_RULES.find(rule => rule.test(desc));
  return foundRule ? foundRule.part : 'unknown';
}

// Anatomical order from top to bottom
const ANATOMICAL_ORDER: BodyPart[] = [
  'head',
  'neck_upper_head',
  'neck_upper',
  'neck',
  'shoulder_upper',
  'shoulder',
  'shoulder_lower',
  'shoulder_lower_2',
  'shoulder_blade_upper',
  'shoulder_blade',
  'shoulder_blade_lower',
  'upper_back',
  'mid_back',
  'lower_back',
  'waist',
  'hips'
];

/**
 * Adds flow direction to each body part group based on immediate transitions
 */
function addFlowDirections(groups: Omit<BodyPartGroup, 'flowDirection'>[]): BodyPartGroup[] {
  if (groups.length === 0) {
    return [];
  }

  const bodyPartRanks = new Map(ANATOMICAL_ORDER.map((part, index) => [part, index]));
  const getRank = (part: BodyPart) => bodyPartRanks.get(part) ?? -1;

  const getDirection = (from: BodyPart, to: BodyPart): FlowDirection => {
    const fromRank = getRank(from);
    const toRank = getRank(to);
    if (fromRank === -1 || toRank === -1) return 'same';
    if (toRank > fromRank) return 'down';
    if (toRank < fromRank) return 'up';
    return 'same';
  };

  const findNextRankedIndex = (startIndex: number): number => {
    for (let i = startIndex; i < groups.length; i++) {
      if (getRank(groups[i].bodyPart) !== -1) return i;
    }
    return -1;
  };

  const findPrevRankedIndex = (startIndex: number): number => {
    for (let i = startIndex; i >= 0; i--) {
      if (getRank(groups[i].bodyPart) !== -1) return i;
    }
    return -1;
  };

  return groups.map((group, index) => {
    const currentRank = getRank(group.bodyPart);

    // Unknown body parts (rank -1) are always "same" level
    if (currentRank === -1) {
      return { ...group, flowDirection: 'same' } as BodyPartGroup;
    }

    let flowDirection: FlowDirection = 'same';

    if (index === 0) {
      // Look ahead for the next ranked body part
      const nextIdx = findNextRankedIndex(index + 1);
      flowDirection = nextIdx !== -1 ? getDirection(group.bodyPart, groups[nextIdx].bodyPart) : 'end';
    } else {
      // Look back for the previous ranked body part
      const prevIdx = findPrevRankedIndex(index - 1);
      if (prevIdx !== -1) {
        flowDirection = getDirection(groups[prevIdx].bodyPart, group.bodyPart);
      } else {
        // If no previous ranked body part exists, this is effectively the "first" ranked group
        // Look ahead to determine direction
        const nextIdx = findNextRankedIndex(index + 1);
        flowDirection = nextIdx !== -1 ? getDirection(group.bodyPart, groups[nextIdx].bodyPart) : 'end';
      }
    }

    return { ...group, flowDirection } as BodyPartGroup;
  });
}

/**
 * Groups a flat list of BodyPartGroups into FlowGroups based on consecutive direction
 */
function groupBodyPartGroupsByFlow(bodyPartGroups: BodyPartGroup[]): FlowGroup[] {
  if (!bodyPartGroups.length) {
    return [];
  }

  return bodyPartGroups.reduce((acc, group) => {
    const lastFlowGroup = acc[acc.length - 1];
    if (lastFlowGroup && lastFlowGroup.direction === group.flowDirection) {
      lastFlowGroup.bodyPartGroups.push(group);
    } else {
      acc.push({
        direction: group.flowDirection,
        bodyPartGroups: [group],
      });
    }
    return acc;
  }, [] as FlowGroup[]);
}

const createBodyPartGroup = (
    bodyPart: BodyPart, 
    steps: any[], 
    startStepNumber: number
): Omit<BodyPartGroup, 'flowDirection'> => {
    const config = BODY_PART_CONFIG[bodyPart];
    const endStepNumber = steps.length > 0
        ? (parseInt(steps[steps.length - 1].step_number) || startStepNumber + steps.length - 1)
        : startStepNumber;

    return {
        bodyPart,
        steps,
        stepCount: steps.length,
        startStepNumber,
        endStepNumber,
        displayName: config.displayName,
        color: config.color,
    };
};

/**
 * Groups program steps by body parts, maintaining consecutive grouping logic
 */
export function groupStepsByBodyPart(steps: any[], subroutines?: any[], allSubroutines?: any[]): ClassificationResult {
  if (!steps || steps.length === 0) {
    return {
      bodyPartGroups: [],
      flowGroups: [],
      subroutineFlows: [],
      totalSteps: 0,
      totalGroups: 0,
      unclassifiedSteps: 0
    };
  }

  // Process main steps (filter out comments and program notes, exclude subroutine steps from main flow)
  const mainSteps = steps.filter(step =>
    step.type !== 'comment' &&
    step.type !== 'program_note' &&
    step.type !== 'subroutine'
  );

  // Process main flow
  const initialGroups: Omit<BodyPartGroup, 'flowDirection'>[] = [];
  let currentBodyPart: BodyPart | null = null;
  let currentGroup: any[] = [];
  let currentStartStep = 0;
  let unclassifiedCount = 0;

  mainSteps.forEach((step, index) => {
    const stepNumber = parseInt(step.step_number) || index + 1; // Fallback to index
    const classifiedBodyPart = classifyStep(step.en_roller_action_description);

    // Inherit body part from the previous step if the current one is 'unknown'
    const bodyPart = classifiedBodyPart === 'unknown' && currentBodyPart
      ? currentBodyPart
      : classifiedBodyPart;

    if (bodyPart === 'unknown') {
      unclassifiedCount++;
    }

    if (bodyPart !== currentBodyPart) {
      if (currentBodyPart) {
        initialGroups.push(createBodyPartGroup(currentBodyPart, currentGroup, currentStartStep));
      }
      currentBodyPart = bodyPart;
      currentGroup = [step];
      currentStartStep = stepNumber;
    } else {
      currentGroup.push(step);
    }
  });

  // Add the last remaining group
  if (currentBodyPart && currentGroup.length > 0) {
    initialGroups.push(createBodyPartGroup(currentBodyPart, currentGroup, currentStartStep));
  }

  const bodyPartGroups = addFlowDirections(initialGroups);
  const flowGroups = groupBodyPartGroupsByFlow(bodyPartGroups);

  // Process subroutines as separate flows
  const subroutineFlows: SubroutineFlow[] = [];

  // First, process subroutine steps from main program flow
  const subroutineSteps = steps.filter(step => step.type === 'subroutine');

  console.log('Subroutine steps found:', subroutineSteps.length);
  console.log('All subroutines available:', allSubroutines?.length || 0);

  if (subroutineSteps.length > 0 && allSubroutines && allSubroutines.length > 0) {
    for (let i = 0; i < subroutineSteps.length; i++) {
      const subroutineStep = subroutineSteps[i];
      const subroutineId = subroutineStep.subroutine_id;

      console.log(`Processing subroutine step ${i + 1}:`, {
        stepNumber: subroutineStep.step_number,
        subroutineId,
        type: subroutineStep.type
      });

      if (subroutineId) {
        // Find the subroutine by matching subroutine_id to subroutineIdJson
        const matchingSubroutine = allSubroutines.find(sub =>
          sub.subroutineIdJson === subroutineId ||
          sub.subroutine_id_json === subroutineId
        );

        console.log('Matching subroutine found:', !!matchingSubroutine, matchingSubroutine?.name);

        if (matchingSubroutine && matchingSubroutine.steps && matchingSubroutine.steps.length > 0) {
          const subroutineAnalysis = groupStepsByBodyPart(matchingSubroutine.steps);

          subroutineFlows.push({
            subroutineId: subroutineId,
            subroutineName: matchingSubroutine.name || `Subroutine ${subroutineId}`,
            stepNumber: subroutineStep.step_number || (i + 1),
            bodyPartGroups: subroutineAnalysis.bodyPartGroups,
            flowGroups: subroutineAnalysis.flowGroups,
            totalSteps: subroutineAnalysis.totalSteps,
            totalGroups: subroutineAnalysis.totalGroups,
            unclassifiedSteps: subroutineAnalysis.unclassifiedSteps
          });

          console.log('Added subroutine flow:', subroutineId);
        }
      }
    }
  }

  // Then, process linked subroutines from programData.subRoutines (if any)
  if (subroutines && subroutines.length > 0) {
    for (let i = 0; i < subroutines.length; i++) {
      const subroutine = subroutines[i];

      if (subroutine && subroutine.steps && subroutine.steps.length > 0) {
        // Recursively analyze the subroutine's steps (without passing subroutines to avoid infinite recursion)
        const subroutineAnalysis = groupStepsByBodyPart(subroutine.steps);

        subroutineFlows.push({
          subroutineId: subroutine.subroutine_id_json || subroutine.subroutineIdJson || subroutine.id?.toString() || `sub_${i}`,
          subroutineName: subroutine.name || `Subroutine ${i + 1}`,
          stepNumber: i + 1, // Order in the subroutines list
          bodyPartGroups: subroutineAnalysis.bodyPartGroups,
          flowGroups: subroutineAnalysis.flowGroups,
          totalSteps: subroutineAnalysis.totalSteps,
          totalGroups: subroutineAnalysis.totalGroups,
          unclassifiedSteps: subroutineAnalysis.unclassifiedSteps
        });
      }
    }
  }

  return {
    bodyPartGroups,
    flowGroups,
    subroutineFlows,
    totalSteps: mainSteps.length,
    totalGroups: bodyPartGroups.length,
    unclassifiedSteps: unclassifiedCount
  };
}

/**
 * Get statistics about body part distribution
 */
export function getBodyPartStats(result: ClassificationResult) {
  const stepCountsByBodyPart = result.bodyPartGroups.reduce((acc, group) => {
    acc[group.bodyPart] = (acc[group.bodyPart] || 0) + group.stepCount;
    return acc;
  }, {} as Record<BodyPart, number>);

  const totalClassified = result.totalSteps - result.unclassifiedSteps;
  const classificationRate = result.totalSteps > 0 
    ? ((totalClassified / result.totalSteps) * 100).toFixed(1)
    : '0';

  return {
    ...stepCountsByBodyPart,
    totalClassified,
    classificationRate,
  };
}
